"""
Тест парсера шаблонов домашних заданий
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.manager_tests.template_parser import parse_homework_template


async def test_parser():
    """Тестирование парсера шаблонов"""
    
    # Тестовый шаблон
    template = """Название ДЗ: ДЗ-1 
Вопрос: Что такое алканы?
Вариант: Углеводороды
Вариант: Спирты  
Вариант: Кислоты
Микротема: 1 
Правильный ответ на вопрос: A
Время ответа на вопрос: 30
Возможность добавить фото к вопросу: +

Вопрос: Формула метана?
Вариант: CH4
Вариант: C2H6
Микротема: 2
Правильный ответ на вопрос: A  
Время ответа на вопрос: 45
Возможность добавить фото к вопросу: -"""

    print("🧪 Тестирование парсера шаблонов...")
    print("📝 Тестовый шаблон:")
    print(template)
    print("\n" + "="*50 + "\n")
    
    # Тестируем парсинг (используем subject_id = 1 для теста)
    success, data, errors = await parse_homework_template(template, subject_id=1)
    
    print(f"✅ Успех: {success}")
    
    if errors:
        print("❌ Ошибки:")
        for error in errors:
            print(f"  - {error}")
    
    if success:
        print("📊 Результат парсинга:")
        print(f"  📄 Название ДЗ: {data['test_name']}")
        print(f"  ❓ Количество вопросов: {len(data['questions'])}")
        
        for i, question in enumerate(data['questions'], 1):
            print(f"\n  🔸 Вопрос {i}:")
            print(f"    📝 Текст: {question['text']}")
            print(f"    📋 Варианты: {question['options']}")
            print(f"    ✅ Правильный ответ: {question['correct_answer']}")
            print(f"    🏷️ Микротема: {question.get('microtopic_number', 'не указана')}")
            print(f"    ⏱️ Время: {question['time_limit']} сек.")
            print(f"    📸 Фото: {'Да' if question['needs_photo'] else 'Нет'}")
    
    print("\n" + "="*50)
    print("🎯 Тест завершен!")


async def test_error_cases():
    """Тестирование обработки ошибок"""
    
    print("\n🧪 Тестирование обработки ошибок...")
    
    # Тест 1: Пустое название
    template1 = """Название ДЗ: 
Вопрос: Тест?
Вариант: A
Вариант: B
Микротема: 1
Правильный ответ на вопрос: A
Время ответа на вопрос: 30
Возможность добавить фото к вопросу: -"""
    
    print("\n📝 Тест 1: Пустое название ДЗ")
    success, data, errors = await parse_homework_template(template1, subject_id=1)
    print(f"✅ Успех: {success}")
    if errors:
        print("❌ Ошибки:")
        for error in errors:
            print(f"  - {error}")
    
    # Тест 2: Мало вариантов ответа
    template2 = """Название ДЗ: Тест-2
Вопрос: Тест?
Вариант: Только один вариант
Микротема: 1
Правильный ответ на вопрос: A
Время ответа на вопрос: 30
Возможность добавить фото к вопросу: -"""
    
    print("\n📝 Тест 2: Мало вариантов ответа")
    success, data, errors = await parse_homework_template(template2, subject_id=1)
    print(f"✅ Успех: {success}")
    if errors:
        print("❌ Ошибки:")
        for error in errors:
            print(f"  - {error}")
    
    # Тест 3: Неправильное время
    template3 = """Название ДЗ: Тест-3
Вопрос: Тест?
Вариант: A
Вариант: B
Микротема: 1
Правильный ответ на вопрос: A
Время ответа на вопрос: 5
Возможность добавить фото к вопросу: -"""
    
    print("\n📝 Тест 3: Неправильное время (меньше 10 сек)")
    success, data, errors = await parse_homework_template(template3, subject_id=1)
    print(f"✅ Успех: {success}")
    if errors:
        print("❌ Ошибки:")
        for error in errors:
            print(f"  - {error}")


if __name__ == "__main__":
    asyncio.run(test_parser())
    asyncio.run(test_error_cases())
